from pathlib import Path
from gpt4all import GPT4All
import json
import random
import string
import os
from evaluation.model_api.BaseAPI import BaseAPI

class GPT4AllAPI(BaseAPI):
    def __init__(self, model_name, generation_config={}):
        """
        Initialize the GPT4All API.

        Args:
            model_name (str): Name of the GPT4All model file.
            generation_config (dict, optional): Configuration for text generation.
        """
        super().__init__(generation_config)
        print(f'Loading GPT4All model: {model_name}...')
        self.model = GPT4All(model_name)
        print('Finish loading')

        self.sys_prompt = self.basic_sys_prompt
        self.no_tool_sys_prompt = """You are a helpful assistant"""

    def get_system_prompt(self, d=None):
        """
        Get the system prompt for the model.

        Args:
            d (dict, optional): Data containing environment information.

        Returns:
            str: System prompt.
        """
        if d is None:
            return self.sys_prompt
        else:
            # Check if no tool is provided
            if not d['environments'] or 'name' not in d['environments'][0] or d['environments'][0]['name'] == '':
                return self.no_tool_sys_prompt
            else:
                return self.sys_prompt

    def parse_tool_str(self, tool_str):
        """
        Parse a tool call string.

        Args:
            tool_str (str): String containing tool call information.

        Returns:
            dict: Parsed tool call information.
        """
        try:
            if '{' not in tool_str or '}' not in tool_str:
                return {'type': 'error', 'message': f'Wrong tool call result: {tool_str}'}

            res = json.loads(tool_str)
            if 'name' not in res or ('arguments' not in res and 'parameters' not in res):
                print(f"Wrong tool call result: {res}")
                return {'type': 'error', 'message': f'Wrong tool call result: {res}'}

            tool_call_id = ''.join(random.sample(string.ascii_letters + string.digits, 9))
            tool_name = res['name']

            if 'parameters' in res:
                res['arguments'] = res['parameters']

            arguments = json.loads(res['arguments']) if isinstance(res['arguments'], str) else res['arguments']
            return {'type': 'tool', 'tool_call_id': tool_call_id, 'tool_name': tool_name, 'arguments': arguments}

        except Exception as e:
            print(f"Error parsing tool string: {e}")
            return {'type': 'error', 'message': f'Wrong tool call result: {tool_str}'}

    def response(self, messages, tools):
        """
        Generate a response from the model.

        Args:
            messages (list): List of message dictionaries.
            tools (list): List of available tools.

        Returns:
            str: Generated response.
        """
        # Format tools for the model if available
        tools_str = ""
        if tools:
            tools_str = "Available tools:\n"
            for tool in tools:
                function = tool['function']
                tools_str += f"Tool name: {function['name']}\n"
                tools_str += f"Description: {function['description']}\n"
                tools_str += "Parameters:\n"
                for param_name, param_info in function['parameters']['properties'].items():
                    required = "required" if param_name in function['parameters'].get('required', []) else "optional"
                    tools_str += f"  - {param_name} ({param_info.get('type', 'any')}): {param_info.get('description', '')} [{required}]\n"
                tools_str += "\n"

        # Format the prompt in the Llama 3 format
        formatted_prompt = ""

        # Add system message
        system_content = "You are a helpful assistant."
        if messages and messages[0]['role'] == 'system':
            system_content = messages[0]['content']

        formatted_prompt += f"<|start_header_id|>system<|end_header_id|>\n\n{system_content}<|eot_id|>"

        # Add conversation history
        for msg in messages:
            if msg['role'] == 'user':
                content = msg['content']
                # If tools are available and this is the last user message, append tools info
                if tools and msg == messages[-1] and messages[-1]['role'] == 'user':
                    content = f"{content}\n\n{tools_str}"
                formatted_prompt += f"<|start_header_id|>user<|end_header_id|>\n\n{content}<|eot_id|>"
            elif msg['role'] == 'assistant':
                if 'tool_calls' in msg:
                    # Format tool calls
                    tool_calls_str = ""
                    for tool_call in msg['tool_calls']:
                        tool_name = tool_call['function']['name']
                        arguments = tool_call['function']['arguments']
                        tool_calls_str += f'{{"name": "{tool_name}", "arguments": {arguments}}}'
                    formatted_prompt += f"<|start_header_id|>assistant<|end_header_id|>\n\n{tool_calls_str}<|eot_id|>"
                else:
                    formatted_prompt += f"<|start_header_id|>assistant<|end_header_id|>\n\n{msg['content']}<|eot_id|>"
            elif msg['role'] == 'tool':
                # Add tool results as system messages
                tool_name = msg.get('name', 'unknown_tool')
                content = msg['content']
                formatted_prompt += f"<|start_header_id|>system<|end_header_id|>\n\nTool {tool_name} returned: {content}<|eot_id|>"

        # Add the assistant prompt for the response
        formatted_prompt += "<|start_header_id|>assistant<|end_header_id|>\n\n"

        # Generate the response
        response = self.model.generate(
            prompt=formatted_prompt,
            max_tokens=self.generation_config.get('max_new_tokens', 2048),
            temp=self.generation_config.get('temperature', 0.7),
            top_k=self.generation_config.get('top_k', 40),
            top_p=self.generation_config.get('top_p', 0.9),
            repeat_penalty=self.generation_config.get('repetition_penalty', 1.1),
            repeat_last_n=64,
            streaming=False
        )

        return response

    def generate_response(self, messages, tools=None):
        """
        Generate a response and parse it for tool calls.

        Args:
            messages (list): List of message dictionaries.
            tools (list): List of available tools.

        Returns:
            dict: Generated response or tool call information.
        """
        completion = self.response(messages, tools)
        print(f'completion: {completion}')
        completion = completion.replace('```json', '').replace('```', '').strip('\n')

        # Check if the response is a tool call
        if self.is_json(completion) and 'name' in self.parse_json(completion):
            res = self.parse_tool_str(completion)
            return res

        # Check if the response contains a tool call embedded in text
        elif '{' in completion and '}' in completion:
            start = completion.index('{')
            end = completion.rindex('}')
            tool_str = completion[start:end + 1]
            tool_str = tool_str.replace("'", '"').replace('"{', '{').replace('}"', '}')

            if self.is_json(tool_str) and 'name' in self.parse_json(tool_str):
                res = self.parse_tool_str(tool_str)
                return res
            else:
                return {'type': 'content', 'content': completion}
        else:
            return {'type': 'content', 'content': completion}