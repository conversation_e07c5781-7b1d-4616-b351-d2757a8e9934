from openai import OpenAI

client = OpenAI(
    base_url="https://openrouter.ai/api/v1",
    api_key="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",
)

try:
    models = client.models.list()
    print("Available models:", models)
except Exception as e:
    print("Error:", e)

# $env:SSL_CERT_FILE="D:\anaconda\envs\agent\Lib\site-packages\certifi\cacert.pem"
# echo $env:SSL_CERT_FILE