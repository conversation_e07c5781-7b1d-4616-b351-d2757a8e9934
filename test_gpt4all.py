import sys
from gpt4all import GPT4All

def main():
    # List available models
    print("Available models:")
    models = GPT4All.list_models()
    for i, model in enumerate(models):
        print(f"{i+1}. {model['name']} ({model['filename']})")

    # Initialize the GPT4All model with the default model
    model_name = "Meta-Llama-3-8B-Instruct.Q4_0.gguf"  # Default model

    # Check if a model name was provided as a command-line argument
    if len(sys.argv) > 1:
        model_name = sys.argv[1]

    print(f"\nTesting GPT4All with model: {model_name}")

    # Initialize the model
    try:
        model = GPT4All(model_name)

        # Simple prompt without using chat session
        system_prompt = "You are a helpful assistant."
        user_prompt = "Tell me a short joke."

        # Format the prompt in the Llama 3 format
        formatted_prompt = f"<|start_header_id|>system<|end_header_id|>\n\n{system_prompt}<|eot_id|><|start_header_id|>user<|end_header_id|>\n\n{user_prompt}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n"

        # Generate a response
        response = model.generate(
            prompt=formatted_prompt,
            max_tokens=1024,
            temp=0.7,
            top_k=40,
            top_p=0.9,
            repeat_penalty=1.1,
            repeat_last_n=64,
            streaming=False
        )

        print("\nResponse:")
        print(response)

    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
